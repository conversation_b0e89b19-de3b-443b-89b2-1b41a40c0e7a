"use client";
import React, { useEffect, useState } from "react";
import ReactECharts from "echarts-for-react";
import Card from "~/component/card";

const PowerVisualization = () => {
  const [chartData, setChartData] = useState({
    power: [] as Array<[number, number]>,
    price: [] as Array<[number, number]>,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).getTime(), // 7 days ago
    end: new Date().getTime(),
  });

  // Separate state for the date inputs
  const [startDate, setStartDate] = useState(
    new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split("T")[0],
  );
  const [endDate, setEndDate] = useState(new Date().toISOString().split("T")[0]);

  const fetchPowerData = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch("/api/power/ou-history", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(dateRange),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch power data");
      }

      const data = await response.json();
      console.log("API Response:", data);
      setChartData(data.power || []);
    } catch (error) {
      console.error("Error fetching power data:", error);
      setError(error instanceof Error ? error.message : "Unknown error occurred");
      setChartData([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPowerData();
  }, [dateRange]);

  const handleDateRangeUpdate = () => {
    const start = new Date(startDate).getTime();
    const end = new Date(endDate + "T23:59:59").getTime(); // Include the full end day

    if (start >= end) {
      setError("Startdatum muss vor dem Enddatum liegen");
      return;
    }

    setDateRange({ start, end });
  };

  const handleQuickSelect = (days: number) => {
    const end = new Date();
    const start = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

    setStartDate(start.toISOString().split("T")[0]);
    setEndDate(end.toISOString().split("T")[0]);
    setDateRange({
      start: start.getTime(),
      end: end.getTime(),
    });
  };

  const getOptions = () => {
    return {
      title: {
        text: "Ladeleistung Visualisierung",
        left: "center",
        textStyle: {
          fontSize: 16,
          fontWeight: "bold",
        },
      },
      brush: {
        toolbox: ["rect", "clear"],
        xAxisIndex: 0,
        brushLink: "all",
        outOfBrush: {
          colorAlpha: 0.1,
        },
        inBrush: {
          colorAlpha: 1,
        },
      },
      toolbox: {
        feature: {
          brush: {
            type: ["rect", "clear"],
          },
          dataZoom: {
            yAxisIndex: "none",
          },
          restore: {},
        },
        right: 20,
        top: 20,
      },
      dataZoom: [
        {
          type: "slider",
          startValue: dateRange.start,
          endValue: dateRange.end,
          realtime: true,
          filterMode: "filter",
          xAxisIndex: 0,
          handleSize: 20,
          bottom: 10,
          onEnd: (params: any) => {
            const startDate = new Date(params.startValue).getTime();
            const endDate = new Date(params.endValue).getTime();
            setDateRange({
              start: startDate,
              end: endDate,
            });
          },
        },
        {
          type: "inside",
          xAxisIndex: 0,
          filterMode: "filter",
        },
      ],
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          crossStyle: {
            color: "#999",
          },
        },
        formatter: (params: any) => {
          if (params && params.length > 0) {
            const timestamp = params[0].data[0];
            const power = params[0].data[1];
            const date = new Date(timestamp).toLocaleString("de-DE");
            return `${date}<br/>Leistung: ${power.toFixed(2)} kW`;
          }
          return "";
        },
      },
      legend: {
        data: ["Ladeleistung (kW)"],
        top: 30,
      },
      grid: {
        top: 80,
        bottom: 80,
        left: 60,
        right: 40,
      },
      xAxis: {
        type: "time",
        name: "Zeit",
        nameLocation: "middle",
        nameGap: 30,
        axisLabel: {
          formatter: (value: number) => {
            return new Date(value).toLocaleDateString("de-DE");
          },
        },
      },
      yAxis: {
        type: "value",
        name: "Leistung (kW)",
        nameLocation: "middle",
        nameGap: 40,
        axisLabel: {
          formatter: "{value} kW",
        },
        min: 0,
      },
      series: [
        {
          name: "Ladeleistung (kW)",
          type: "line",
          data: chartData,
          smooth: true,
          lineStyle: {
            width: 2,
            color: "#3b82f6",
          },
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "rgba(59, 130, 246, 0.3)",
                },
                {
                  offset: 1,
                  color: "rgba(59, 130, 246, 0.1)",
                },
              ],
            },
          },
          symbolSize: 4,
        },
      ],
    };
  };

  if (loading) {
    return (
      <Card>
        <div className="flex h-96 items-center justify-center">
          <div className="border-primary h-32 w-32 animate-spin rounded-full border-b-2"></div>
        </div>
      </Card>
    );
  }

  return (
    <Card>
      <div className="space-y-4">
        <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
          <h2 className="text-xl font-bold text-primary">Ladeleistung Visualisierung</h2>
        </div>

        {/* Date Range Selection */}
        <div className="space-y-4 rounded-lg border border-gray-200 bg-gray-50 p-4">
          <h3 className="text-lg font-semibold text-gray-700">Zeitraum auswählen</h3>

          {/* Quick Select Buttons */}
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => handleQuickSelect(1)}
              className="rounded bg-primary px-3 py-1 text-sm text-white hover:bg-blue-600 focus:shadow-soft-primary-outline focus:outline-none"
            >
              Heute
            </button>
            <button
              onClick={() => handleQuickSelect(7)}
              className="rounded bg-primary px-3 py-1 text-sm text-white hover:bg-blue-600 focus:shadow-soft-primary-outline focus:outline-none"
            >
              7 Tage
            </button>
            <button
              onClick={() => handleQuickSelect(30)}
              className="rounded bg-primary px-3 py-1 text-sm text-white hover:bg-blue-600 focus:shadow-soft-primary-outline focus:outline-none"
            >
              30 Tage
            </button>
            <button
              onClick={() => handleQuickSelect(90)}
              className="rounded bg-primary px-3 py-1 text-sm text-white hover:bg-blue-600 focus:shadow-soft-primary-outline focus:outline-none"
            >
              90 Tage
            </button>
          </div>

          {/* Custom Date Range */}
          <div className="flex flex-col gap-4 sm:flex-row sm:items-end">
            <div className="flex-1">
              <label htmlFor="startDate" className="block text-sm font-medium text-gray-700">
                Von
              </label>
              <input
                type="date"
                id="startDate"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:shadow-soft-primary-outline focus:outline-none"
              />
            </div>
            <div className="flex-1">
              <label htmlFor="endDate" className="block text-sm font-medium text-gray-700">
                Bis
              </label>
              <input
                type="date"
                id="endDate"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="mt-1 block w-full rounded-md border border-gray-300 px-3 py-2 focus:shadow-soft-primary-outline focus:outline-none"
              />
            </div>
            <button
              onClick={handleDateRangeUpdate}
              disabled={loading}
              className="hover:bg-primary/90 rounded bg-primary px-4 py-2 text-white focus:shadow-soft-primary-outline focus:outline-none disabled:opacity-50"
            >
              Aktualisieren
            </button>
          </div>

          <div className="text-sm text-gray-600">
            Aktueller Zeitraum: {new Date(dateRange.start).toLocaleDateString("de-DE")} -{" "}
            {new Date(dateRange.end).toLocaleDateString("de-DE")}
          </div>
        </div>

        {error ? (
          <div className="flex h-96 items-center justify-center text-red-500">
            <div className="text-center">
              <p className="font-semibold">Fehler beim Laden der Daten</p>
              <p className="text-sm">{error}</p>
            </div>
          </div>
        ) : chartData.length === 0 ? (
          <div className="flex h-96 items-center justify-center text-gray-500">
            Keine Daten für den ausgewählten Zeitraum verfügbar
          </div>
        ) : (
          <div className="h-96">
            <ReactECharts
              option={getOptions()}
              style={{ height: "100%", width: "100%" }}
              className="h-full"
            />
          </div>
        )}

        <div className="text-sm text-gray-500">
          <p>• Verwenden Sie den Slider unten im Diagramm, um den Zeitbereich anzupassen</p>
          <p>• Fehlende 5-Minuten-Intervalle werden mit 0 kW angezeigt</p>
          <p>
            • Die Daten werden für Ihre ausgewählte Organisationseinheit und deren Untereinheiten
            aggregiert
          </p>
        </div>
      </div>
    </Card>
  );
};

export default PowerVisualization;
