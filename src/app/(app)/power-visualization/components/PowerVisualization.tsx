"use client";
import React, { useEffect, useState } from "react";
import ReactECharts from "echarts-for-react";
import Card from "~/component/card";

const PowerVisualization = () => {
  const [chartData, setChartData] = useState<Array<[number, number]>>([]);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).getTime(), // 7 days ago
    end: new Date().getTime(),
  });

  const fetchPowerData = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/power/ou-history", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(dateRange),
      });
      
      if (!response.ok) {
        throw new Error("Failed to fetch power data");
      }
      
      const data = await response.json();
      setChartData(data.power || []);
    } catch (error) {
      console.error("Error fetching power data:", error);
      setChartData([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPowerData();
  }, [dateRange]);

  const getOptions = () => {
    return {
      title: {
        text: "Ladeleistung Visualisierung",
        left: "center",
        textStyle: {
          fontSize: 16,
          fontWeight: "bold",
        },
      },
      dataZoom: [
        {
          type: "slider",
          startValue: dateRange.start,
          endValue: dateRange.end,
          realtime: true,
          filterMode: "filter",
          xAxisIndex: 0,
          handleSize: 20,
          bottom: 10,
          onEnd: (params: any) => {
            const startDate = new Date(params.startValue).getTime();
            const endDate = new Date(params.endValue).getTime();
            setDateRange({
              start: startDate,
              end: endDate,
            });
          },
        },
      ],
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          crossStyle: {
            color: "#999",
          },
        },
        formatter: (params: any) => {
          if (params && params.length > 0) {
            const timestamp = params[0].data[0];
            const power = params[0].data[1];
            const date = new Date(timestamp).toLocaleString("de-DE");
            return `${date}<br/>Leistung: ${power.toFixed(2)} kW`;
          }
          return "";
        },
      },
      legend: {
        data: ["Ladeleistung (kW)"],
        top: 30,
      },
      grid: {
        top: 80,
        bottom: 80,
        left: 60,
        right: 40,
      },
      xAxis: {
        type: "time",
        name: "Zeit",
        nameLocation: "middle",
        nameGap: 30,
        axisLabel: {
          formatter: (value: number) => {
            return new Date(value).toLocaleDateString("de-DE");
          },
        },
      },
      yAxis: {
        type: "value",
        name: "Leistung (kW)",
        nameLocation: "middle",
        nameGap: 40,
        axisLabel: {
          formatter: "{value} kW",
        },
        min: 0,
      },
      series: [
        {
          name: "Ladeleistung (kW)",
          type: "line",
          data: chartData,
          smooth: true,
          lineStyle: {
            width: 2,
            color: "#3b82f6",
          },
          areaStyle: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: "rgba(59, 130, 246, 0.3)",
                },
                {
                  offset: 1,
                  color: "rgba(59, 130, 246, 0.1)",
                },
              ],
            },
          },
          symbolSize: 4,
        },
      ],
    };
  };

  if (loading) {
    return (
      <Card>
        <div className="flex h-96 items-center justify-center">
          <div className="h-32 w-32 animate-spin rounded-full border-b-2 border-primary"></div>
        </div>
      </Card>
    );
  }

  return (
    <Card>
      <div className="space-y-4">
        <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
          <h2 className="text-xl font-bold text-primary">Ladeleistung Visualisierung</h2>
          <div className="text-sm text-gray-600">
            Zeitraum: {new Date(dateRange.start).toLocaleDateString("de-DE")} - {new Date(dateRange.end).toLocaleDateString("de-DE")}
          </div>
        </div>
        
        {chartData.length === 0 ? (
          <div className="flex h-96 items-center justify-center text-gray-500">
            Keine Daten für den ausgewählten Zeitraum verfügbar
          </div>
        ) : (
          <div className="h-96">
            <ReactECharts
              option={getOptions()}
              style={{ height: "100%", width: "100%" }}
              className="h-full"
            />
          </div>
        )}
        
        <div className="text-sm text-gray-500">
          <p>• Verwenden Sie den Slider unten im Diagramm, um den Zeitbereich anzupassen</p>
          <p>• Fehlende 5-Minuten-Intervalle werden mit 0 kW angezeigt</p>
          <p>• Die Daten werden für Ihre ausgewählte Organisationseinheit und deren Untereinheiten aggregiert</p>
        </div>
      </div>
    </Card>
  );
};

export default PowerVisualization;
