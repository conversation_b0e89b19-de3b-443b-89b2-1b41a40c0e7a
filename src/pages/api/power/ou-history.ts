import type { NextApiRequest, NextApiResponse } from "next";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import { getOusBelowOu } from "~/server/model/ou/func";

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.selectedOu) {
      return res.status(401).json({ error: "No session or selected OU" });
    }

    const { start, end } = JSON.parse(req.body);
    
    // Get all OUs below the selected OU (including the selected OU itself)
    const ous = await getOusBelowOu(session.user.selectedOu);
    const ouIds = ous.map(ou => ou.id);

    // Fetch power data for the selected OU and its children
    const result = await prisma.historyPowerByOu.findMany({
      where: {
        ouId: { in: ouIds },
        timestamp: { 
          gte: new Date(start),
          lte: new Date(end)
        },
      },
      orderBy: {
        timestamp: "asc",
      },
      select: {
        timestamp: true,
        power: true,
      },
    });

    // Group by timestamp and sum power values
    const powerByTimestamp = new Map<string, number>();
    
    result.forEach(record => {
      const timestampKey = record.timestamp.getTime().toString();
      const currentPower = powerByTimestamp.get(timestampKey) || 0;
      powerByTimestamp.set(timestampKey, currentPower + record.power);
    });

    // Generate all 5-minute intervals between start and end
    const startDate = new Date(start);
    const endDate = new Date(end);
    const intervals: Array<[number, number]> = [];
    
    const current = new Date(startDate);
    while (current <= endDate) {
      const timestamp = current.getTime();
      const power = powerByTimestamp.get(timestamp.toString()) || 0;
      intervals.push([timestamp, power / 1000]); // Convert to kW
      
      // Add 5 minutes
      current.setMinutes(current.getMinutes() + 5);
    }

    const data = {
      power: intervals,
    };

    res.status(200).json(data);
  } catch (error) {
    console.error("Error fetching OU power history:", error);
    res.status(500).json({ error: "Internal server error" });
  }
}
